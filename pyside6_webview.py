#!/usr/bin/env python3
"""
使用PySide6 + QWebEngine创建现代桌面浏览器窗口
显示指定大小的窗口并加载百度网站
"""

import sys
import os

# 添加用户安装路径到sys.path
import site
user_site = site.getusersitepackages()
if user_site not in sys.path:
    sys.path.insert(0, user_site)

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QStatusBar
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtCore import QUrl, Qt
from PySide6.QtGui import QIcon, QAction

class ModernWebBrowser(QMainWindow):
    def __init__(self, width=1080, height=1920, url="https://www.baidu.com"):
        super().__init__()
        
        # 设置窗口属性
        self.setWindowTitle("百度搜索 - PySide6 WebEngine")
        self.setGeometry(100, 100, width, height)  # x, y, width, height
        
        # 设置窗口大小限制
        self.setMinimumSize(300, 400)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 创建WebEngine视图
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)
        
        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("准备加载网页...")
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 加载网页
        self.web_view.load(QUrl(url))
        
        # 连接信号
        self.web_view.loadStarted.connect(self.on_load_started)
        self.web_view.loadFinished.connect(self.on_load_finished)
        self.web_view.loadProgress.connect(self.on_load_progress)
        self.web_view.titleChanged.connect(self.on_title_changed)
        
        print(f"PySide6窗口已创建: {width}x{height}")
        print(f"加载网址: {url}")
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        # 刷新动作
        refresh_action = QAction('刷新', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_page)
        file_menu.addAction(refresh_action)
        
        # 退出动作
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
    
    def refresh_page(self):
        """刷新页面"""
        self.web_view.reload()
        self.status_bar.showMessage("正在刷新页面...")
    
    def on_load_started(self):
        """页面开始加载"""
        print("开始加载页面...")
        self.setWindowTitle("百度搜索 - 加载中...")
        self.status_bar.showMessage("正在加载页面...")
    
    def on_load_finished(self, success):
        """页面加载完成"""
        if success:
            print("页面加载成功!")
            self.setWindowTitle("百度搜索 - PySide6 WebEngine")
            self.status_bar.showMessage("页面加载完成")
        else:
            print("页面加载失败!")
            self.setWindowTitle("百度搜索 - 加载失败")
            self.status_bar.showMessage("页面加载失败")
    
    def on_load_progress(self, progress):
        """页面加载进度"""
        if progress < 100:
            self.setWindowTitle(f"百度搜索 - 加载中 ({progress}%)")
            self.status_bar.showMessage(f"加载进度: {progress}%")
    
    def on_title_changed(self, title):
        """页面标题改变"""
        if title:
            self.setWindowTitle(f"{title} - PySide6 WebEngine")

def main():
    print("=" * 60)
    print("PySide6 + QWebEngine 现代桌面浏览器")
    print("=" * 60)
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("PySide6 Modern WebBrowser")
        app.setApplicationVersion("1.0")
        
        # 设置应用程序属性
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建主窗口
        # 您可以在这里修改窗口大小和URL
        window = ModernWebBrowser(
            width=1080,      # 窗口宽度
            height=1920,     # 窗口高度
            url="https://www.baidu.com"  # 要加载的网址
        )
        
        # 显示窗口
        window.show()
        
        print("PySide6应用程序已启动!")
        print("窗口配置:")
        print(f"  - 大小: {window.width()}x{window.height()}")
        print(f"  - 标题: {window.windowTitle()}")
        print("  - 可调整大小: 是")
        print("  - 最小尺寸: 300x400")
        print("  - 菜单栏: 是")
        print("  - 状态栏: 是")
        print()
        print("功能:")
        print("  - F5: 刷新页面")
        print("  - Ctrl+Q: 退出应用")
        print("  - 关闭窗口: 退出应用")
        print()
        print("应用程序正在运行，请查看桌面窗口...")
        
        # 运行应用程序事件循环
        exit_code = app.exec()
        print(f"PySide6应用程序已退出，退出码: {exit_code}")
        return exit_code
        
    except ImportError as e:
        print(f"导入PySide6模块失败: {e}")
        print()
        print("请安装PySide6:")
        print("python3 -m pip install PySide6 --user")
        return 1
        
    except Exception as e:
        print(f"启动应用时出现错误: {e}")
        print()
        print("可能的解决方案:")
        print("1. 确保已安装 PySide6")
        print("2. 确保在图形界面环境中运行")
        print("3. 检查 DISPLAY 环境变量")
        
        return 1

if __name__ == "__main__":
    main()
