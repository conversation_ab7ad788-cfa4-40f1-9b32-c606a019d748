#!/usr/bin/env python3
"""
虚拟环境版本：使用PySide6 + QWebEngine创建桌面浏览器窗口
基于Python 3.8虚拟环境，显示指定大小的窗口并加载百度网站
"""

import sys
import os
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QStatusBar, QMenuBar, QToolBar, QLineEdit, QPushButton, QHBoxLayout)
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtCore import QUrl, Qt, Signal
from PySide6.QtGui import QIcon, QAction, QKeySequence

class VenvWebBrowser(QMainWindow):
    def __init__(self, width=540, height=720, url="https://www.baidu.com"):
        super().__init__()
        
        # 设置窗口属性
        self.setWindowTitle("百度搜索 - PySide6 虚拟环境版")
        self.setGeometry(100, 100, width, height)
        
        # 设置窗口大小限制
        self.setMinimumSize(300, 400)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建地址栏
        self.create_address_bar()
        main_layout.addWidget(self.address_widget)
        
        # 创建WebEngine视图
        self.web_view = QWebEngineView()
        main_layout.addWidget(self.web_view)

        # 连接导航按钮信号（现在web_view已创建）
        self.back_btn.clicked.connect(self.web_view.back)
        self.forward_btn.clicked.connect(self.web_view.forward)
        self.refresh_btn.clicked.connect(self.web_view.reload)

        # 连接历史信号（使用定时器定期更新按钮状态）
        from PySide6.QtCore import QTimer
        self.history_timer = QTimer()
        self.history_timer.timeout.connect(self.update_navigation_buttons)
        self.history_timer.start(500)  # 每500ms更新一次

        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("准备加载网页...")

        # 创建菜单栏
        self.create_menu_bar()

        # 连接信号
        self.web_view.loadStarted.connect(self.on_load_started)
        self.web_view.loadFinished.connect(self.on_load_finished)
        self.web_view.loadProgress.connect(self.on_load_progress)
        self.web_view.titleChanged.connect(self.on_title_changed)
        self.web_view.urlChanged.connect(self.on_url_changed)
        
        # 加载初始网页
        self.load_url(url)
        
        print(f"虚拟环境PySide6窗口已创建: {width}x{height}")
        print(f"初始网址: {url}")
    
    def create_address_bar(self):
        """创建地址栏"""
        self.address_widget = QWidget()
        layout = QHBoxLayout()
        self.address_widget.setLayout(layout)
        
        # 地址输入框
        self.address_bar = QLineEdit()
        self.address_bar.setPlaceholderText("输入网址...")
        self.address_bar.returnPressed.connect(self.navigate_to_url)
        layout.addWidget(self.address_bar)
        
        # 前进后退按钮
        self.back_btn = QPushButton("←")
        self.back_btn.setMaximumWidth(40)
        self.back_btn.setEnabled(False)
        layout.insertWidget(0, self.back_btn)

        self.forward_btn = QPushButton("→")
        self.forward_btn.setMaximumWidth(40)
        self.forward_btn.setEnabled(False)
        layout.insertWidget(1, self.forward_btn)

        # 刷新按钮
        self.refresh_btn = QPushButton("⟳")
        self.refresh_btn.setMaximumWidth(40)
        layout.addWidget(self.refresh_btn)
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        # 新窗口
        new_action = QAction('新窗口', self)
        new_action.setShortcut(QKeySequence.New)
        new_action.triggered.connect(self.new_window)
        file_menu.addAction(new_action)
        
        file_menu.addSeparator()
        
        # 刷新
        refresh_action = QAction('刷新', self)
        refresh_action.setShortcut(QKeySequence.Refresh)
        refresh_action.triggered.connect(self.web_view.reload)
        file_menu.addAction(refresh_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction('退出', self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 导航菜单
        nav_menu = menubar.addMenu('导航')
        
        back_action = QAction('后退', self)
        back_action.setShortcut(QKeySequence.Back)
        back_action.triggered.connect(self.web_view.back)
        nav_menu.addAction(back_action)
        
        forward_action = QAction('前进', self)
        forward_action.setShortcut(QKeySequence.Forward)
        forward_action.triggered.connect(self.web_view.forward)
        nav_menu.addAction(forward_action)
    
    def navigate_to_url(self):
        """导航到地址栏中的URL"""
        url = self.address_bar.text().strip()
        if url:
            self.load_url(url)
    
    def load_url(self, url):
        """加载URL"""
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        self.web_view.load(QUrl(url))
    
    def new_window(self):
        """打开新窗口"""
        new_window = VenvWebBrowser()
        new_window.show()

    def update_navigation_buttons(self):
        """更新导航按钮状态"""
        try:
            history = self.web_view.page().history()
            self.back_btn.setEnabled(history.canGoBack())
            self.forward_btn.setEnabled(history.canGoForward())
        except:
            pass  # 忽略错误
    
    def on_load_started(self):
        """页面开始加载"""
        print("开始加载页面...")
        self.status_bar.showMessage("正在加载页面...")
    
    def on_load_finished(self, success):
        """页面加载完成"""
        if success:
            print("页面加载成功!")
            self.status_bar.showMessage("页面加载完成")
        else:
            print("页面加载失败!")
            self.status_bar.showMessage("页面加载失败")
    
    def on_load_progress(self, progress):
        """页面加载进度"""
        if progress < 100:
            self.status_bar.showMessage(f"加载进度: {progress}%")
    
    def on_title_changed(self, title):
        """页面标题改变"""
        if title:
            self.setWindowTitle(f"{title} - PySide6 虚拟环境版")
    
    def on_url_changed(self, url):
        """URL改变时更新地址栏"""
        self.address_bar.setText(url.toString())

def main():
    print("=" * 70)
    print("PySide6 + QWebEngine 虚拟环境桌面浏览器")
    print("=" * 70)
    print(f"Python版本: {sys.version}")
    print(f"虚拟环境: {sys.prefix}")
    print()
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("PySide6 Venv WebBrowser")
        app.setApplicationVersion("1.0")
        
        # 设置应用程序属性
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建主窗口
        window = VenvWebBrowser(
            width=540,      # 窗口宽度
            height=1920,     # 窗口高度
            url="https://www.baidu.com"  # 要加载的网址
        )
        
        # 显示窗口
        window.show()
        
        print("虚拟环境PySide6应用程序已启动!")
        print("窗口配置:")
        print(f"  - 大小: {window.width()}x{window.height()}")
        print(f"  - 标题: {window.windowTitle()}")
        print("  - 可调整大小: 是")
        print("  - 最小尺寸: 300x400")
        print("  - 地址栏: 是")
        print("  - 导航按钮: 是")
        print("  - 菜单栏: 是")
        print("  - 状态栏: 是")
        print()
        print("功能:")
        print("  - 地址栏导航")
        print("  - 前进/后退按钮")
        print("  - F5: 刷新页面")
        print("  - Ctrl+N: 新窗口")
        print("  - Ctrl+Q: 退出应用")
        print()
        print("应用程序正在运行，请查看桌面窗口...")
        
        # 运行应用程序事件循环
        exit_code = app.exec()
        print(f"虚拟环境PySide6应用程序已退出，退出码: {exit_code}")
        return exit_code
        
    except ImportError as e:
        print(f"导入PySide6模块失败: {e}")
        print("请确保在虚拟环境中安装了PySide6:")
        print("source venv/bin/activate && pip install PySide6")
        return 1
        
    except Exception as e:
        print(f"启动应用时出现错误: {e}")
        print()
        print("可能的解决方案:")
        print("1. 确保已激活虚拟环境")
        print("2. 确保已安装 PySide6")
        print("3. 确保在图形界面环境中运行")
        
        return 1

if __name__ == "__main__":
    main()
