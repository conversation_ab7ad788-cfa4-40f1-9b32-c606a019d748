#!/usr/bin/env python3
"""
使用PyQt5 + QWebEngine创建桌面浏览器窗口
显示指定大小的窗口并加载百度网站
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtCore import QUrl, Qt
from PyQt5.QtGui import QIcon

class WebBrowserWindow(QMainWindow):
    def __init__(self, width=1080, height=1920, url="https://www.baidu.com"):
        super().__init__()
        
        # 设置窗口属性
        self.setWindowTitle("百度搜索 - PyQt5 WebEngine")
        self.setGeometry(100, 100, width, height)  # x, y, width, height
        
        # 设置窗口大小限制
        self.setMinimumSize(300, 400)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 创建WebEngine视图
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)
        
        # 加载网页
        self.web_view.load(QUrl(url))
        
        # 连接信号
        self.web_view.loadStarted.connect(self.on_load_started)
        self.web_view.loadFinished.connect(self.on_load_finished)
        self.web_view.loadProgress.connect(self.on_load_progress)
        
        print(f"窗口已创建: {width}x{height}")
        print(f"加载网址: {url}")
    
    def on_load_started(self):
        """页面开始加载"""
        print("开始加载页面...")
        self.setWindowTitle("百度搜索 - 加载中...")
    
    def on_load_finished(self, success):
        """页面加载完成"""
        if success:
            print("页面加载成功!")
            self.setWindowTitle("百度搜索 - PyQt5 WebEngine")
        else:
            print("页面加载失败!")
            self.setWindowTitle("百度搜索 - 加载失败")
    
    def on_load_progress(self, progress):
        """页面加载进度"""
        if progress < 100:
            self.setWindowTitle(f"百度搜索 - 加载中 ({progress}%)")

def main():
    print("=" * 60)
    print("PyQt5 + QWebEngine 桌面浏览器")
    print("=" * 60)

    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("PyQt5 WebBrowser")

        # 设置应用程序属性（在创建QApplication后设置）
        try:
            app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        except:
            pass  # 忽略高DPI设置错误

        # 创建主窗口
        # 您可以在这里修改窗口大小和URL
        window = WebBrowserWindow(
            width=1080,      # 窗口宽度
            height=1920,     # 窗口高度
            url="https://www.baidu.com"  # 要加载的网址
        )

        # 显示窗口
        window.show()

        print("应用程序已启动!")
        print("窗口配置:")
        print(f"  - 大小: {window.width()}x{window.height()}")
        print(f"  - 标题: {window.windowTitle()}")
        print("  - 可调整大小: 是")
        print("  - 最小尺寸: 300x400")
        print()
        print("提示: 关闭窗口或按 Ctrl+C 退出应用")
        print("应用程序正在运行，请查看桌面窗口...")

        # 运行应用程序事件循环
        exit_code = app.exec_()
        print(f"应用程序已退出，退出码: {exit_code}")
        return exit_code

    except ImportError as e:
        print(f"导入PyQt5模块失败: {e}")
        print()
        print("请安装PyQt5:")
        print("sudo apt install python3-pyqt5 python3-pyqt5.qtwebengine")
        return 1

    except Exception as e:
        print(f"启动应用时出现错误: {e}")
        print()
        print("可能的解决方案:")
        print("1. 确保已安装 python3-pyqt5 和 python3-pyqt5.qtwebengine")
        print("2. 确保在图形界面环境中运行")
        print("3. 检查 DISPLAY 环境变量")

        return 1

if __name__ == "__main__":
    main()
