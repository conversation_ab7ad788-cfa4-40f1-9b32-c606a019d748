#!/usr/bin/env python3
"""
创建一个本地web服务器，模拟450*720窗口显示百度网站
由于当前环境没有GUI支持，这个版本会启动一个本地服务器
用户可以在浏览器中访问来查看效果
"""

from flask import Flask, render_template_string
import webbrowser
import threading
import time
import sys

app = Flask(__name__)

# HTML模板，模拟450*720窗口
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PyWebView 模拟 - 450x720窗口</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .window-container {
            width: 450px;
            height: 720px;
            background-color: white;
            border: 2px solid #ccc;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        .title-bar {
            background-color: #e9ecef;
            padding: 8px 12px;
            border-bottom: 1px solid #ddd;
            font-size: 14px;
            font-weight: bold;
            color: #333;
            display: flex;
            align-items: center;
        }
        .window-controls {
            margin-left: auto;
            display: flex;
            gap: 8px;
        }
        .control-btn {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
        }
        .close { background-color: #ff5f57; }
        .minimize { background-color: #ffbd2e; }
        .maximize { background-color: #28ca42; }
        .content {
            flex: 1;
            position: relative;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .info-panel {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            background-color: rgba(255, 255, 255, 0.95);
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            font-size: 12px;
            z-index: 1000;
        }
        .hide-info {
            display: none;
        }
        @media (max-width: 500px) {
            .window-container {
                width: 95vw;
                height: 95vh;
            }
        }
    </style>
    <script>
        function hideInfo() {
            document.querySelector('.info-panel').classList.add('hide-info');
        }
        
        // 5秒后自动隐藏信息面板
        setTimeout(hideInfo, 5000);
    </script>
</head>
<body>
    <div class="window-container">
        <div class="title-bar">
            <span>百度 - PyWebView 模拟</span>
            <div class="window-controls">
                <button class="control-btn minimize"></button>
                <button class="control-btn maximize"></button>
                <button class="control-btn close"></button>
            </div>
        </div>
        <div class="content">
            <div class="info-panel">
                <strong>PyWebView 窗口模拟</strong><br>
                窗口大小: 450x720 像素<br>
                目标网站: https://www.baidu.com<br>
                <small>点击任意位置或等待5秒后此信息将消失</small>
                <button onclick="hideInfo()" style="float: right; font-size: 10px;">隐藏</button>
            </div>
            <iframe src="https://www.baidu.com" 
                    title="百度搜索"
                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox">
                <p>您的浏览器不支持iframe。请直接访问 <a href="https://www.baidu.com">百度</a></p>
            </iframe>
        </div>
    </div>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

def start_server():
    """启动Flask服务器"""
    print("=" * 60)
    print("PyWebView 模拟器")
    print("=" * 60)
    print("由于当前环境没有GUI支持，我们创建了一个web版本的模拟器")
    print("这个模拟器会显示一个450x720像素的窗口，内嵌百度网站")
    print()
    print("服务器启动中...")
    
    try:
        # 启动Flask服务器
        app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
    except Exception as e:
        print(f"启动服务器时出错: {e}")

def main():
    print("正在启动PyWebView模拟器...")
    
    # 在后台线程中启动服务器
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # 等待服务器启动
    time.sleep(3)
    
    print("\n服务器已启动!")
    print("请在浏览器中访问: http://127.0.0.1:5000")
    print("您将看到一个模拟的450x720窗口，显示百度网站")
    print()
    
    # 尝试自动打开浏览器
    try:
        print("正在尝试自动打开浏览器...")
        webbrowser.open('http://127.0.0.1:5000')
        print("浏览器已打开!")
    except Exception as e:
        print(f"无法自动打开浏览器: {e}")
        print("请手动在浏览器中访问: http://127.0.0.1:5000")
    
    print("\n按 Ctrl+C 停止服务器")
    
    try:
        # 保持主线程运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n服务器已停止，感谢使用!")
        sys.exit(0)

if __name__ == '__main__':
    main()
