#!/usr/bin/env python3
"""
使用pywebview创建一个450*720的窗口并打开百度网站
"""

import webview
import os

def main():
    try:
        # 创建webview窗口
        # width=450, height=720 设置窗口大小
        # url='https://www.baidu.com' 设置要打开的网址
        # title='百度' 设置窗口标题
        webview.create_window(
            title='百度',
            url='https://www.baidu.com',
            width=450,
            height=720,
            resizable=True,  # 允许调整窗口大小
            min_size=(300, 400)  # 设置最小窗口大小
        )

        # 启动webview
        print("正在启动webview窗口...")
        print("窗口大小: 450x720")
        print("网址: https://www.baidu.com")
        webview.start(debug=False)

    except Exception as e:
        print(f"启动webview时出现错误: {e}")
        print("这可能是由于缺少GUI环境或相关依赖导致的。")
        print("在无GUI环境下，您可以使用浏览器直接访问: https://www.baidu.com")

        # 尝试使用系统默认浏览器打开
        import webbrowser
        try:
            print("尝试使用系统默认浏览器打开...")
            webbrowser.open('https://www.baidu.com')
        except Exception as browser_error:
            print(f"无法打开浏览器: {browser_error}")

if __name__ == '__main__':
    main()
