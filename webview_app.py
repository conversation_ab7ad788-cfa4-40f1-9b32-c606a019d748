#!/usr/bin/env python3
"""
使用pywebview创建一个450*720的桌面窗口并打开百度网站
"""

import sys
import os

# 添加系统python包路径以使用系统的gi模块
sys.path.insert(0, '/usr/lib/python3/dist-packages')

import webview

def main():
    try:
        print("正在启动PyWebView桌面应用...")
        print("窗口大小: 450x720")
        print("网址: https://www.baidu.com")

        # 创建webview窗口
        # width=450, height=720 设置窗口大小
        # url='https://www.baidu.com' 设置要打开的网址
        # title='百度' 设置窗口标题
        webview.create_window(
            title='百度 - PyWebView',
            url='https://www.baidu.com',
            width=1080,
            height=1980,
            resizable=True,  # 允许调整窗口大小
            min_size=(300, 400),  # 设置最小窗口大小
            maximized=False,  # 不最大化启动
            on_top=False  # 不置顶
        )

        # 启动webview
        print("窗口正在启动...")
        webview.start(debug=False)

    except Exception as e:
        print(f"启动webview时出现错误: {e}")
        print("错误详情:", str(e))
        print("\n可能的解决方案:")
        print("1. 确保您在图形界面环境中运行此程序")
        print("2. 安装必要的GUI依赖包")
        print("3. 检查显示环境变量 DISPLAY")

        # 尝试使用系统默认浏览器打开
        import webbrowser
        try:
            print("\n尝试使用系统默认浏览器打开...")
            webbrowser.open('https://www.baidu.com')
            print("已在浏览器中打开百度网站")
        except Exception as browser_error:
            print(f"无法打开浏览器: {browser_error}")

if __name__ == '__main__':
    main()
