#!/usr/bin/env python3
"""
优化版本：PySide6 + QWebEngine桌面浏览器
减少WebGL错误，专注于基本浏览功能
"""

import sys
import os

# 设置环境变量减少错误输出
os.environ['QT_LOGGING_RULES'] = 'qt.webenginecontext.debug=false'
os.environ['QTWEBENGINE_CHROMIUM_FLAGS'] = '--disable-gpu --disable-software-rasterizer --disable-dev-shm-usage --no-sandbox'

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                               QStatusBar, QLineEdit, QPushButton, QHBoxLayout)
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtCore import QUrl, Qt
from PySide6.QtGui import QAction, QKeySequence

class CleanWebBrowser(QMainWindow):
    def __init__(self, width=540, height=720, url="https://www.baidu.com"):
        super().__init__()
        
        # 设置窗口属性
        self.setWindowTitle("百度搜索 - 优化版PySide6")
        self.setGeometry(100, 100, width, height)
        self.setMinimumSize(300, 400)
        
        # 创建中央widget和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建简化的地址栏
        self.create_simple_toolbar()
        main_layout.addWidget(self.toolbar_widget)
        
        # 创建WebEngine视图
        self.web_view = QWebEngineView()
        main_layout.addWidget(self.web_view)
        
        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("准备就绪")
        
        # 创建简化菜单
        self.create_simple_menu()
        
        # 连接信号
        self.web_view.loadFinished.connect(self.on_load_finished)
        self.web_view.titleChanged.connect(self.on_title_changed)
        self.web_view.urlChanged.connect(self.on_url_changed)
        
        # 加载初始网页
        self.load_url(url)
        
        print(f"优化版PySide6窗口已创建: {width}x{height}")
    
    def create_simple_toolbar(self):
        """创建简化的工具栏"""
        self.toolbar_widget = QWidget()
        layout = QHBoxLayout()
        self.toolbar_widget.setLayout(layout)
        
        # 后退按钮
        self.back_btn = QPushButton("←")
        self.back_btn.setMaximumWidth(35)
        self.back_btn.setToolTip("后退")
        self.back_btn.clicked.connect(self.go_back)
        layout.addWidget(self.back_btn)
        
        # 前进按钮
        self.forward_btn = QPushButton("→")
        self.forward_btn.setMaximumWidth(35)
        self.forward_btn.setToolTip("前进")
        self.forward_btn.clicked.connect(self.go_forward)
        layout.addWidget(self.forward_btn)
        
        # 刷新按钮
        self.refresh_btn = QPushButton("⟳")
        self.refresh_btn.setMaximumWidth(35)
        self.refresh_btn.setToolTip("刷新")
        self.refresh_btn.clicked.connect(self.refresh_page)
        layout.addWidget(self.refresh_btn)
        
        # 地址栏
        self.address_bar = QLineEdit()
        self.address_bar.setPlaceholderText("输入网址或搜索...")
        self.address_bar.returnPressed.connect(self.navigate_to_url)
        layout.addWidget(self.address_bar)
        
        # 转到按钮
        self.go_btn = QPushButton("转到")
        self.go_btn.setMaximumWidth(50)
        self.go_btn.clicked.connect(self.navigate_to_url)
        layout.addWidget(self.go_btn)
    
    def create_simple_menu(self):
        """创建简化菜单"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        refresh_action = QAction('刷新', self)
        refresh_action.setShortcut(QKeySequence.Refresh)
        refresh_action.triggered.connect(self.refresh_page)
        file_menu.addAction(refresh_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出', self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
    
    def load_url(self, url):
        """加载URL"""
        if not url.startswith(('http://', 'https://')):
            if '.' in url:
                url = 'https://' + url
            else:
                # 如果不是网址，使用百度搜索
                url = f'https://www.baidu.com/s?wd={url}'
        
        self.web_view.load(QUrl(url))
        self.address_bar.setText(url)
    
    def navigate_to_url(self):
        """导航到地址栏中的URL"""
        url = self.address_bar.text().strip()
        if url:
            self.load_url(url)
    
    def go_back(self):
        """后退"""
        if self.web_view.page().history().canGoBack():
            self.web_view.back()
    
    def go_forward(self):
        """前进"""
        if self.web_view.page().history().canGoForward():
            self.web_view.forward()
    
    def refresh_page(self):
        """刷新页面"""
        self.web_view.reload()
        self.status_bar.showMessage("正在刷新...")
    
    def on_load_finished(self, success):
        """页面加载完成"""
        if success:
            self.status_bar.showMessage("页面加载完成")
        else:
            self.status_bar.showMessage("页面加载失败")
    
    def on_title_changed(self, title):
        """页面标题改变"""
        if title:
            self.setWindowTitle(f"{title} - 优化版PySide6")
    
    def on_url_changed(self, url):
        """URL改变时更新地址栏"""
        self.address_bar.setText(url.toString())

def main():
    print("=" * 60)
    print("优化版 PySide6 + QWebEngine 桌面浏览器")
    print("=" * 60)
    print("特点: 减少错误输出，专注基本功能")
    print()
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("Clean PySide6 Browser")
        
        # 创建主窗口
        window = CleanWebBrowser(
            width=540,       # 窗口宽度
            height=720,      # 窗口高度
            url="https://www.baidu.com"
        )
        
        # 显示窗口
        window.show()
        
        print("优化版应用程序已启动!")
        print("窗口配置:")
        print(f"  - 大小: {window.width()}x{window.height()}")
        print("  - 功能: 地址栏、导航按钮、菜单")
        print("  - 优化: 减少WebGL错误输出")
        print()
        print("使用说明:")
        print("  - 在地址栏输入网址或搜索词")
        print("  - 使用导航按钮前进/后退")
        print("  - F5刷新页面，Ctrl+Q退出")
        print()
        
        # 运行应用程序
        exit_code = app.exec()
        print(f"应用程序已退出，退出码: {exit_code}")
        return exit_code
        
    except Exception as e:
        print(f"启动失败: {e}")
        return 1

if __name__ == "__main__":
    main()
