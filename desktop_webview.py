#!/usr/bin/env python3
"""
使用系统Python和PyWebView创建450*720桌面窗口显示百度网站
"""

import sys
import subprocess
import os

def install_pywebview_system():
    """在系统Python中安装pywebview"""
    try:
        print("正在检查系统Python中的pywebview...")
        import webview
        print("pywebview已安装")
        return True
    except ImportError:
        print("pywebview未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pywebview', '--user'])
            print("pywebview安装成功")

            # 添加用户安装路径到sys.path
            import site
            user_site = site.getusersitepackages()
            if user_site not in sys.path:
                sys.path.insert(0, user_site)

            # 重新尝试导入
            try:
                import webview
                print("pywebview导入成功")
                return True
            except ImportError as e:
                print(f"pywebview导入失败: {e}")
                return False

        except subprocess.CalledProcessError as e:
            print(f"安装pywebview失败: {e}")
            return False

def main():
    print("=" * 60)
    print("PyWebView 桌面应用启动器")
    print("=" * 60)
    print("目标: 创建450x720窗口显示百度网站")
    print()
    
    # 检查并安装pywebview
    if not install_pywebview_system():
        print("无法安装pywebview，退出...")
        return
    
    try:
        # 重新导入webview
        import webview
        
        print("正在启动PyWebView桌面窗口...")
        print("窗口配置:")
        print("  - 标题: 百度搜索")
        print("  - 大小: 450x720 像素")
        print("  - 网址: https://www.baidu.com")
        print("  - 可调整大小: 是")
        print()
        
        # 创建webview窗口
        webview.create_window(
            title='百度搜索',
            url='https://www.baidu.com',
            width=1080,
            height=1920,
            resizable=True,
            min_size=(300, 400),
            maximized=False
        )
        
        print("启动窗口...")
        print("注意: 如果窗口没有出现，请检查您的桌面环境")
        
        # 启动webview
        webview.start(debug=False)
        
        print("应用程序已关闭")
        
    except ImportError as e:
        print(f"导入webview失败: {e}")
        print("请确保已正确安装pywebview及其依赖")
        
    except Exception as e:
        print(f"启动应用时出现错误: {e}")
        print()
        print("故障排除建议:")
        print("1. 确保您在桌面环境中运行（不是SSH或纯终端）")
        print("2. 检查DISPLAY环境变量:", os.environ.get('DISPLAY', '未设置'))
        print("3. 尝试安装额外的GUI依赖:")
        print("   sudo apt install python3-gi python3-gi-cairo gir1.2-gtk-3.0 gir1.2-webkit2-4.0")
        print("4. 或者安装Qt依赖:")
        print("   sudo apt install python3-pyqt5 python3-pyqt5.qtwebengine")
        
        # 作为备选方案，打开浏览器
        print()
        print("作为备选方案，正在打开系统浏览器...")
        try:
            import webbrowser
            webbrowser.open('https://www.baidu.com')
            print("已在浏览器中打开百度网站")
        except Exception as browser_error:
            print(f"无法打开浏览器: {browser_error}")

if __name__ == '__main__':
    main()
