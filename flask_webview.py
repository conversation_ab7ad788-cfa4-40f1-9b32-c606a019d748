#!/usr/bin/env python3
"""
使用Flask创建本地服务器，然后用pywebview显示
这样可以避免直接访问外部网站时的一些问题
"""

import webview
import threading
import time
from flask import Flask, render_template_string

app = Flask(__name__)

# HTML模板，包含一个iframe来显示百度
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百度搜索</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        .header {
            background-color: #f8f9fa;
            padding: 10px;
            text-align: center;
            border-bottom: 1px solid #ddd;
        }
        .iframe-container {
            width: 100%;
            height: calc(100vh - 60px);
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>百度搜索 - PyWebView 演示</h2>
        <div class="info">窗口大小: 450x720 | 使用PyWebView创建</div>
    </div>
    <div class="iframe-container">
        <iframe src="https://www.baidu.com" 
                title="百度搜索"
                sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox">
        </iframe>
    </div>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

def start_flask():
    """在后台线程中启动Flask服务器"""
    app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)

def main():
    try:
        # 在后台线程中启动Flask服务器
        flask_thread = threading.Thread(target=start_flask, daemon=True)
        flask_thread.start()
        
        # 等待Flask服务器启动
        time.sleep(2)
        
        # 创建webview窗口
        print("正在启动webview窗口...")
        print("窗口大小: 450x720")
        print("本地服务器: http://127.0.0.1:5000")
        
        webview.create_window(
            title='百度搜索 - PyWebView',
            url='http://127.0.0.1:5000',
            width=450,
            height=720,
            resizable=True,
            min_size=(300, 400)
        )
        
        # 启动webview
        webview.start(debug=False)
        
    except Exception as e:
        print(f"启动webview时出现错误: {e}")
        print("这可能是由于缺少GUI环境或相关依赖导致的。")
        
        # 尝试使用系统默认浏览器打开
        import webbrowser
        try:
            print("尝试使用系统默认浏览器打开...")
            print("请在浏览器中访问: http://127.0.0.1:5000")
            
            # 启动Flask服务器
            flask_thread = threading.Thread(target=start_flask, daemon=True)
            flask_thread.start()
            time.sleep(2)
            
            webbrowser.open('http://127.0.0.1:5000')
            
            # 保持服务器运行
            print("Flask服务器正在运行，按Ctrl+C退出...")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n服务器已停止")
                
        except Exception as browser_error:
            print(f"无法打开浏览器: {browser_error}")

if __name__ == '__main__':
    main()
